import { Typography } from '@components/typography';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';

type DealsFeatureCarouselProps<T extends { id?: string | number }> = {
  cards: T[];
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  renderCard: (card: T) => React.ReactNode;
};

export const CardsCarousel = <T extends { id?: string | number }>({
  title,
  cards,
  actionBtn,
  renderCard,
}: DealsFeatureCarouselProps<T>) => {
  const isMobileView = useIsMobileView();

  if (!cards?.length) {
    return null;
  }

  return (
    <Carousel
      className={'w-full overflow-hidden'}
      opts={{
        loop: true,
        align: 'start',
        duration: 20,
        slidesToScroll: 2,
      }}
    >
      <div className={'mb-6 grid grid-flow-col items-center px-6 md:px-0'}>
        <Typography
          tag="h2"
          variant={isMobileView ? 'xxs' : 'xs'}
          className={'flex items-center gap-3'}
        >
          {title}
        </Typography>

        <div className={'relative ml-auto '}>
          {actionBtn && actionBtn}

          {!isMobileView && (
            <>
              <CarouselPrevious
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
              <CarouselNext
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
            </>
          )}
        </div>
      </div>
      <CarouselContent isInteractive className="-ml-0 md:-ml-6 mr-5 flex py-4">
        {cards.map((card, index) => (
          <CarouselItem key={card.id ?? index} className="basis-[15rem] pl-6">
            {renderCard(card)}
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};
