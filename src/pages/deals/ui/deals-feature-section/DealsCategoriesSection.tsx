import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { useIsMobileView } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
  isDealCategory,
} from '../../config';
import { DealsNoResult } from '../deals-no-results-section/DealsNoResults';
import { DealsList } from '../DealsList';

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsCategorySection = ({ className }: { className?: string }) => {
  const { title, category } = routeApi.useSearch();

  const { data } = useGetDeals({ title, categories: category });

  const isMobileView = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  if (!data) {
    return <DealsNoResult className={cn(className)} />;
  }

  const Icon = getDealCategoryIcon(category || '');

  const getCategoryDisplayName = () => {
    if (category && isDealCategory(category)) {
      const translationKey = getDealCategoryTranslationKey(category);
      return t(translationKey);
    }
    return category;
  };

  return (
    <div className={cn(className)}>
      {isMobileView && category ? (
        <div className="mb-6 flex items-center gap-2">
          <Icon className="h-5 w-5" />
          <Typography variant="xxs" affects="semibold">
            {getCategoryDisplayName()}
          </Typography>
        </div>
      ) : null}

      <DealsList data={data} />
    </div>
  );
};
