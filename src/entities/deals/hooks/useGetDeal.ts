import { LANGUAGE_ABBREVIATION_BY_SHORTNAME } from '@entities/languages';
import { isDealCategory } from '@pages/deals/config';
import { formatDate } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

import {
  type DealQueryVariables,
  useSuspenseDealQuery,
} from '../api/queries.gen';

export const useGetDeal = (variables: DealQueryVariables) => {
  const { i18n } = useTranslation();

  return useSuspenseDealQuery(variables, {
    select: (data) => {
      if (!data?.deal) {
        return null;
      }

      const deal = data.deal;
      const dealTitle = deal?.translations?.find(
        (translation) =>
          translation?.language ===
          LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
      )?.deal_title;

      const dealDescription = deal?.translations?.find(
        (translation) =>
          translation?.language ===
          LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
      )?.description;

      const dealDiscountLabel = deal?.translations?.find(
        (translation) =>
          translation?.language ===
          LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
      )?.discount_label;

      const dealTrackingUrl = deal?.translations?.find(
        (translation) =>
          translation?.language ===
          LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
      )?.tracking_url;

      const dealCategoryName = isDealCategory(deal?.category_name)
        ? deal.category_name
        : undefined;

      return {
        id: deal?.id,
        title: dealTitle,
        description: dealDescription,
        discountLabel: dealDiscountLabel,
        trackingUrl: dealTrackingUrl,
        imageUrl: deal?.image_path,
        categoryName: dealCategoryName,
        promocode: deal?.promocode,
        merchantName: deal?.merchant?.name,
        merchantLogo: deal?.merchant?.logo_path,
        endTime: formatDate(deal?.end_time, 'DD.MM.YYYY'),
      };
    },
  });
};
