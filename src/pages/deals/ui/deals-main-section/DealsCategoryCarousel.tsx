import { Button } from '@components/ui/button';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { getDealCategoryIcon } from '@pages/deals/config';
import type { DealCategoryName } from '@pages/deals/types';
import { DealCard } from '@pages/deals/ui/DealCard';
import { getRouteApi } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

import { CardsCarousel } from '../../../../shared/components/ui/CardsCarousel';

const routeApi = getRouteApi('/_protected/_main/deals');

type DealsCategoryCarouselProps = {
  category: DealCategoryName;
  title: string;
};

export const DealsCategoryCarousel = ({
  category,
  title,
}: DealsCategoryCarouselProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const { data: deals } = useGetDeals({ categories: category });
  const navigate = routeApi.useNavigate();

  if (!deals) {
    return null;
  }

  const Icon = getDealCategoryIcon(category);

  const onFeatureSectionRedirect = () => {
    navigate({
      search: {
        category: category,
      },
      resetScroll: false,
    });

    setTimeout(() => {
      const element = document.querySelector(`[data-category="${category}"]`);
      if (element) {
        element.scrollIntoView();
      }
    }, 100);
  };

  return (
    <div className={'mb-20 grid gap-1 px-0 md:px-12'}>
      <CardsCarousel
        cards={deals}
        title={
          <button
            type="button"
            className="flex items-center gap-3 hover:text-neutral-700"
            onClick={onFeatureSectionRedirect}
          >
            <Icon />
            {title}
          </button>
        }
        actionBtn={
          <Button
            onClick={onFeatureSectionRedirect}
            size="small"
            variant="black"
            className={'text-center'}
          >
            {t(LOCIZE_DEALS_KEYS.dealsViewAll)}
          </Button>
        }
        renderCard={(card) => {
          return <DealCard deal={card} />;
        }}
      />
    </div>
  );
};
