import { Typography } from '@components/typography';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { cn } from '@utils/tailwind';
import { useTranslation } from 'react-i18next';

import { DealsOrderBy, Direction } from '@/shared/types';

import { DealsList } from '../DealsList';

export const DealsNoResult = ({ className }: { className?: string }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { data } = useGetDeals({
    orderBy: DealsOrderBy.CREATED_AT,
    direction: Direction.DESC,
    limit: 10,
  });

  if (!data?.length) {
    return null;
  }
  return (
    <div className={cn(className, 'flex h-full w-full flex-col')}>
      <Typography variant="text-s" affects="semibold" className="mb-6">
        {t(LOCIZE_DEALS_KEYS.noResults)}
      </Typography>
      <DealsList data={data} />
    </div>
  );
};
