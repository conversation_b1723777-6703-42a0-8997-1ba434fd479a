export const LOCIZE_DEALS_KEYS = {
  kuldan: 'kuldan',
  siniseValguse: 'sinise-valguse',
  sportlik: 'sportlik',
  upgreat: 'upgreat',
  fitlap: 'fitlap',
  mokykline: 'mokykline',
  wolt: 'wolt',
  mlily: 'mlily',
  given: 'given',
  goldenLombard: 'golden-lombard',
  madrat<PERSON>pood: 'madratsipood',
  airsoftgo: 'airsoftgo',
  msl: 'msl',
  rokokoLV: 'rokoko-lv',
  ekoplanet: 'ekoplanet',
  ekoplanetLV: 'ekoplanet-lv',
  ekoplanetLT: 'ekoplanet-lv',
  elixHealth: 'elix-health',
  bilance: 'bilance',
  alexanto: 'alexanto',
  merevarustus: 'merevarustus',
  inpuit: 'inpuit',
  anixShop: 'anix-shop',
  lightConcept: 'light-concept',
  bilanceDiscount: 'bilance.discount',
  bigEco1: 'big-eco-1',
  bigEco2: 'big-eco-2',
  jysk: 'jysk',
  detailerPlace: 'detailer-place',
  dialogDiscountDescriptionBigEco1: 'dialog.discount-description.big-eco-1',
  dialogDiscountDescriptionBigEco2: 'dialog.discount-description.big-eco-2',
  dialogDiscountDescriptionJysk: 'dialog.discount-description.jysk',
  dialogDiscountDescriptionDetailerPlace:
    'dialog.discount-description.detailer-place',
  dialogDescription: 'dialog.description',
  dialogDescription2: 'dialog.description-2',
  dialogCopyDisclaimer: 'dialog.copy-disclaimer',
  dialogCopyButton: 'dialog.copy-button.default',
  dialogCopyButtonSuccess: 'dialog.copy-button.success',
  dialogRedirectButton: 'dialog.redirect-button',

  deals: 'deals',
  dealsSearch: 'deals.search',
  dealsViewAll: 'deals.view-all',
  dealsCategories: 'deals.categories',
  showingResultsFor: 'showing-results-for',
  pageTitle: 'page-title',
  title: 'title',
  description: 'description',
  carousel: {
    slide1: {
      title: 'carousel.slide-1.title',
      ctaLabel: 'carousel.slide-1.cta-label',
    },
    slide2: {
      title: 'carousel.slide-2.title',
      ctaLabel: 'carousel.slide-2.cta-label',
    },
    slide3: {
      title: 'carousel.slide-3.title',
      ctaLabel: 'carousel.slide-3.cta-label',
    },
  },
  deal: {
    campaignEndTimeDisclaimer: 'deal.campaign-end-time-disclaimer',
    discountCodeDisclaimer: 'deal.discount-code-disclaimer',
    orderConvenienceDisclaimer: 'deal.order-convenience-disclaimer',
    copyButtonLabel: 'deal.copy-button-label',
    ctaButtonLabel: 'deal.cta-button-label',
  },
  categories: {
    homeImprovement: 'home-improvement',
    bikesAndScooters: 'bikes-and-scooters',
    forCarOwners: 'for-car-owners',
    garden: 'garden',
    kids: 'kids',
    travel: 'travel',
    sport: 'sport',
    pets: 'pets',
    other: 'other',
    clothing: 'clothing',
    jewelry: 'jewelry',
    homeAndLiving: 'home-and-living',
    healthAndWellness: 'health-and-wellness',
    gifts: 'gifts',
    furniture: 'furniture',
    finance: 'finance',
    electronics: 'electronics',
    cosmetics: 'cosmetics',
  },
  noResults: 'no-results',
  relatedDeals: 'related-deals',
  featuredDeals: 'featured-deals',
};
