import { Button } from '@components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@components/ui/dropdown-menu';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetCategories } from '@entities/deals/hooks/useGetCategories';
import { cn } from '@utils/tailwind';
import { Check, Tag } from 'lucide-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
  isDealCategory,
} from '../config';
import type { NullableDealCategoryValue } from '../types';

type DealsCategoriesSelectDesktopProps = {
  onSelect: (value: NullableDealCategoryValue) => void;
  value: NullableDealCategoryValue;
};

export const DealsCategoriesSelectDesktop = ({
  onSelect,
  value,
}: DealsCategoriesSelectDesktopProps) => {
  const { data: categories } = useGetCategories();
  const [open, setOpen] = React.useState(false);

  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  if (!categories || categories.length === 0) {
    return null;
  }

  const selectCategory = (value: NullableDealCategoryValue) => {
    setOpen(false);
    onSelect(value);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-full justify-between rounded-full border px-6 py-6 md:w-fit',
            open || value ? 'border-black' : 'border-color',
          )}
        >
          <div className="flex items-center truncate">
            <Tag className="mr-2 h-5 w-5 shrink-0" />
            <span className="truncate">
              {t(LOCIZE_DEALS_KEYS.dealsCategories)}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="h-[32rem] w-[17rem] overflow-y-auto "
        align="start"
      >
        <DropdownMenuGroup>
          {categories.map((category) => {
            if (!category?.name) {
              return null;
            }

            if (!isDealCategory(category?.name)) {
              return null;
            }

            const categoryName = category.name;

            const isSelected = value === categoryName;
            const Icon = getDealCategoryIcon(categoryName);
            const translationKey = getDealCategoryTranslationKey(categoryName);
            return (
              <DropdownMenuItem
                key={category?.name}
                className={cn(
                  'flex cursor-pointer items-center justify-between px-3 py-3',
                  isSelected && 'bg-accent',
                )}
                onSelect={() => {
                  selectCategory(categoryName);
                }}
              >
                <div className="flex items-center">
                  <Icon className="mr-2 h-5 w-5 shrink-0" />
                  <span>{t(translationKey)}</span>
                </div>
                {isSelected && <Check className="h-4 w-4" />}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
