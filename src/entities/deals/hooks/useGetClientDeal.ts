import { useTranslation } from 'react-i18next';

import { DEAL } from '../config';
import type { DealId } from '../types';

export const useGetClientDeal = () => {
  const { i18n } = useTranslation();

  const getDeal = (dealId: DealId) => {
    const deal = DEAL[dealId];

    if (typeof deal.websiteUrl === 'object') {
      return {
        ...deal,
        websiteUrl: deal.websiteUrl[i18n.language],
      };
    }

    return deal;
  };

  return getDeal;
};
