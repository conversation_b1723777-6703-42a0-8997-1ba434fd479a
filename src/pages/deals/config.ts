import { LOCIZE_DEALS_KEYS } from '@config/locize';
import {
  Baby,
  Bike,
  Building2,
  Car,
  Dog,
  Droplets,
  Dumbbell,
  Euro,
  Gem,
  Gift,
  Hammer,
  Heart,
  HelpCircle,
  type LucideIcon,
  MonitorSmartphone,
  Plane,
  Shirt,
  Sofa,
  Tag,
  Tent,
} from 'lucide-react';

import { DealCategoryName } from './types';

export const DEAL_CATEGORY_ICONS: Record<DealCategoryName, LucideIcon> = {
  [DealCategoryName.BIKES_AND_SCOOTERS]: Bike,
  [DealCategoryName.CLOTHING]: Shirt,
  [DealCategoryName.COSMETICS]: Droplets,
  [DealCategoryName.ELECTRONICS]: MonitorSmartphone,
  [DealCategoryName.FINANCE]: Euro,
  [DealCategoryName.FOR_CAR_OWNERS]: Car,
  [DealCategoryName.FURNITURE]: Sofa,
  [DealCategoryName.GARDEN]: Tent,
  [DealCategoryName.GIFTS]: Gift,
  [DealCategoryName.HEALTH_AND_WELLNESS]: Heart,
  [DealCategoryName.HOME_AND_LIVING]: Building2,
  [DealCategoryName.HOME_IMPROVEMENT]: Hammer,
  [DealCategoryName.JEWELRY]: Gem,
  [DealCategoryName.KIDS]: Baby,
  [DealCategoryName.OTHER]: HelpCircle,
  [DealCategoryName.PETS]: Dog,
  [DealCategoryName.SPORTS]: Dumbbell,
  [DealCategoryName.TRAVEL]: Plane,
} as const;

export const DEAL_CATEGORY_TRANSLATION_KEYS: Record<DealCategoryName, string> =
  {
    [DealCategoryName.BIKES_AND_SCOOTERS]:
      LOCIZE_DEALS_KEYS.categories.bikesAndScooters,
    [DealCategoryName.CLOTHING]: LOCIZE_DEALS_KEYS.categories.clothing,
    [DealCategoryName.COSMETICS]: LOCIZE_DEALS_KEYS.categories.cosmetics,
    [DealCategoryName.ELECTRONICS]: LOCIZE_DEALS_KEYS.categories.electronics,
    [DealCategoryName.FINANCE]: LOCIZE_DEALS_KEYS.categories.finance,
    [DealCategoryName.FOR_CAR_OWNERS]:
      LOCIZE_DEALS_KEYS.categories.forCarOwners,
    [DealCategoryName.FURNITURE]: LOCIZE_DEALS_KEYS.categories.furniture,
    [DealCategoryName.GARDEN]: LOCIZE_DEALS_KEYS.categories.garden,
    [DealCategoryName.GIFTS]: LOCIZE_DEALS_KEYS.categories.gifts,
    [DealCategoryName.HEALTH_AND_WELLNESS]:
      LOCIZE_DEALS_KEYS.categories.healthAndWellness,
    [DealCategoryName.HOME_AND_LIVING]:
      LOCIZE_DEALS_KEYS.categories.homeAndLiving,
    [DealCategoryName.HOME_IMPROVEMENT]:
      LOCIZE_DEALS_KEYS.categories.homeImprovement,
    [DealCategoryName.JEWELRY]: LOCIZE_DEALS_KEYS.categories.jewelry,
    [DealCategoryName.KIDS]: LOCIZE_DEALS_KEYS.categories.kids,
    [DealCategoryName.OTHER]: LOCIZE_DEALS_KEYS.categories.other,
    [DealCategoryName.PETS]: LOCIZE_DEALS_KEYS.categories.pets,
    [DealCategoryName.SPORTS]: LOCIZE_DEALS_KEYS.categories.sport,
    [DealCategoryName.TRAVEL]: LOCIZE_DEALS_KEYS.categories.travel,
  } as const;

export const isDealCategory = (
  category: string,
): category is DealCategoryName => {
  const categoryValues: Set<string> = new Set(Object.values(DealCategoryName));
  return categoryValues.has(category);
};

export const getDealCategoryIcon = (category: string): LucideIcon => {
  if (isDealCategory(category)) {
    return DEAL_CATEGORY_ICONS[category];
  }
  return Tag;
};

export const getDealCategoryTranslationKey = (
  category: DealCategoryName,
): string => {
  return DEAL_CATEGORY_TRANSLATION_KEYS[category];
};
