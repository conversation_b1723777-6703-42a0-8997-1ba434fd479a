import { Helmet } from '@components/Helmet';
import { Typography } from '@components/typography';
import { Dialog } from '@components/ui/dialog';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { MarketingSearchParamValue } from '@config/search-params';
import { useGetAnalyticsCampaignParametersFromSearch } from '@entities/analytics';
import { CreditLineCardSkeleton } from '@entities/credit-line';
import { useFirebase } from '@entities/firebase';
import { useFeatureToggles } from '@hooks/system';
import i18n from '@lib/i18Next';
import { getRouteApi } from '@tanstack/react-router';
import { addMarketingParamsToUrl } from '@utils/url';
import {
  ActiveAgreementsList,
  ActiveAgreementsSkeleton,
} from '@widgets/active-agreements';
import { DealsList } from '@widgets/deals/list';
import { LoanOffers, LoanOffersSkeleton } from '@widgets/loan-offers';
import { lazy, Suspense, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useIsSubscribeNewslettersPopupShown } from '../hooks/useIsSubscribeNewslettersPopupShown';
import { DashboardCreditLineBalance } from './dashboard-credit-line-balance';
import {
  DashboardSubscriptionsCarousel,
  DashboardSubscriptionsCarouselSkeleton,
} from './dashboard-subscriptions-carousel';
import styles from './DashboardPage.module.css';

const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then((module) => ({
    default: module.SubscribeNewsletterPopup,
  })),
);
const DealsDialog = lazy(() =>
  import('@widgets/deals/dialog').then((module) => ({
    default: module.DealsDialog,
  })),
);

const routeApi = getRouteApi('/_protected/_main/dashboard');

export const DashboardPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);
  const { agreementsFeature } = useFeatureToggles();
  const { requestNotificationPermission } = useFirebase();

  const { selectedDeal } = routeApi.useSearch();
  const navigate = routeApi.useNavigate();
  const currentUtmParams = useGetAnalyticsCampaignParametersFromSearch();

  const { isSubscribeNewslettersPopupShown, closeNewslettersPopup } =
    useIsSubscribeNewslettersPopupShown();

  const isDialogOpened = useMemo(
    () => isSubscribeNewslettersPopupShown || !!selectedDeal,
    [isSubscribeNewslettersPopupShown, selectedDeal],
  );

  const handleDialogClose = useCallback(
    (open: boolean) => {
      if (open) {
        return;
      }

      if (isSubscribeNewslettersPopupShown) {
        closeNewslettersPopup();
      }

      navigate({
        replace: true,
        resetScroll: false,
      });
    },
    [navigate, isSubscribeNewslettersPopupShown, closeNewslettersPopup],
  );

  const homepageDealsUrl = addMarketingParamsToUrl({
    url: APP_CONFIG.homepageDeals[i18n.language],
    currentUtmParams,
    fallbackUtmSource: MarketingSearchParamValue.UTM_SOURCE_CUSTOMER_PROFILE,
  });

  useEffect(() => {
    requestNotificationPermission();
  }, []);

  return (
    <>
      <Helmet title={t(LOCIZE_DASHBOARD_KEYS.pageTitle)} />
      <div className={styles.page}>
        <Suspense fallback={<CreditLineCardSkeleton />}>
          <DashboardCreditLineBalance />
        </Suspense>
        <div className={styles.container}>
          <div className={styles.pageBlockScrollable}>
            <div className={styles.dealsHeading}>
              <Typography tag="h2" variant="xs">
                {t(LOCIZE_DASHBOARD_KEYS.dealsHeading)}
              </Typography>
              <a
                className={styles.link}
                target="_blank"
                rel="noreferrer"
                href={homepageDealsUrl}
              >
                <Typography variant="text-s">
                  {t(LOCIZE_DASHBOARD_KEYS.moreDeals)}
                </Typography>
              </a>
            </div>
            <div className="grid gap-4">
              <DealsList />
            </div>
          </div>
          <Suspense fallback={<DashboardSubscriptionsCarouselSkeleton />}>
            <DashboardSubscriptionsCarousel />
          </Suspense>
          <Suspense fallback={<LoanOffersSkeleton />}>
            <LoanOffers />
          </Suspense>
          {agreementsFeature ? (
            <Suspense fallback={<ActiveAgreementsSkeleton />}>
              <ActiveAgreementsList />
            </Suspense>
          ) : null}
          <Dialog open={isDialogOpened} onOpenChange={handleDialogClose}>
            <Suspense>
              {!!selectedDeal && <DealsDialog dealId={selectedDeal} />}
              {isSubscribeNewslettersPopupShown && <SubscribeNewsletterPopup />}
            </Suspense>
          </Dialog>
        </div>
      </div>
    </>
  );
};
