import { DealCard } from '@pages/deals/ui/DealCard';
import { cn } from '@utils/tailwind';

import type { Deal } from '../types';

type DealListProps = {
  data: Deal[];
};

export const DealsList = ({ data }: DealListProps) => {
  const lgGridClassNames =
    data.length < 4 ? 'lg:grid-cols-[repeat(auto-fit,15rem)]' : '';

  return (
    <div
      className={cn(
        'grid w-full grid-cols-[repeat(auto-fill,minmax(9.95rem,1fr))] gap-6 md:grid-cols-[repeat(auto-fill,minmax(240px,1fr))]',
        lgGridClassNames,
      )}
    >
      {data.map((deal: Deal) => (
        <DealCard key={deal.id} deal={deal} />
      ))}
    </div>
  );
};
