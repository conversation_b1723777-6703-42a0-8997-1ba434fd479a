import { Typography } from '@components/typography';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@components/ui/breadcrumb';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import {
  getDealCategoryTranslationKey,
  isDealCategory,
} from '@pages/deals/config';
import type { DealCategoryName } from '@pages/deals/types';
import { Link } from '@tanstack/react-router';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type DealBreadcrumbProps = {
  category?: DealCategoryName;
  title?: string;
  className?: string;
};

export const DealBreadcrumb = ({
  category,
  title,
  className = '',
}: DealBreadcrumbProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const getCategoryDisplayName = (category: DealCategoryName) => {
    if (category && isDealCategory(category)) {
      const translationKey = getDealCategoryTranslationKey(category);
      return t(translationKey);
    }
    return category;
  };

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to={ROUTE_NAMES.deals}>
              <Typography
                variant="text-s"
                affects="semibold"
                className="text-gray-500"
              >
                {t(LOCIZE_DEALS_KEYS.deals)}
              </Typography>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator>
          <ChevronRight className="h-4 w-4" />
        </BreadcrumbSeparator>

        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to={ROUTE_NAMES.deals} search={{ category }}>
              <Typography
                variant="text-s"
                affects="semibold"
                className="text-gray-500"
              >
                {category && getCategoryDisplayName(category)}
              </Typography>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator>
          <ChevronRight className="h-4 w-4" />
        </BreadcrumbSeparator>

        <BreadcrumbItem>
          <BreadcrumbPage>
            <Typography
              variant="text-s"
              affects="semibold"
              className="text-primary-black"
            >
              {title}
            </Typography>
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};
