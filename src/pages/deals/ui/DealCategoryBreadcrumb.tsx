import { Typography } from '@components/typography';
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@components/ui/breadcrumb';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { getRouteApi, Link } from '@tanstack/react-router';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { getDealCategoryTranslationKey, isDealCategory } from '../config';

type DealCategoryBreadcrumbProps = {
  className?: string;
};

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealCategoryBreadcrumb = ({
  className,
}: DealCategoryBreadcrumbProps) => {
  const { title, category } = routeApi.useSearch();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const navigate = routeApi.useNavigate();

  const isMainDealsPage = !title && !category;

  const getBreadcrumbText = () => {
    if (title) {
      return `${t(LOCIZE_DEALS_KEYS.showingResultsFor)} ${title}`;
    }

    if (category && isDealCategory(category)) {
      const translationKey = getDealCategoryTranslationKey(category);
      return t(translationKey);
    }

    return category || '';
  };

  const query = getBreadcrumbText();

  const handleDealsClick = () => {
    navigate({
      replace: true,
      search: {
        title: undefined,
        category: undefined,
      },
    });
  };

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link onClick={handleDealsClick}>
              <Typography
                variant="text-s"
                affects="semibold"
                className={
                  isMainDealsPage ? 'text-primary-black' : 'text-gray-500'
                }
              >
                {t(LOCIZE_DEALS_KEYS.deals)}
              </Typography>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator>
          <ChevronRight className="h-4 w-4" />
        </BreadcrumbSeparator>

        {query && (
          <BreadcrumbItem>
            <BreadcrumbPage>
              <Typography
                variant="text-s"
                affects="semibold"
                className="text-primary-black"
              >
                {query}
              </Typography>
            </BreadcrumbPage>
          </BreadcrumbItem>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
};
